"""
Test the enhanced app with new adaptive learning features.
"""
import asyncio
import json
from mcp import ClientSession
from mcp.client.sse import sse_client

SERVER_URL = "http://localhost:8000/sse"

async def extract_response_content(response):
    """Helper function to extract content from MCP response (same as app.py)"""
    if isinstance(response, dict):
        return response
    
    if hasattr(response, 'content') and isinstance(response.content, list):
        for item in response.content:
            if hasattr(item, 'text') and item.text:
                try:
                    return json.loads(item.text)
                except Exception as e:
                    return {"raw_pretty": item.text, "parse_error": str(e)}
    
    if isinstance(response, str):
        try:
            return json.loads(response)
        except Exception:
            return {"raw_pretty": response}
    
    return {"raw_pretty": str(response), "type": type(response).__name__}

async def test_enhanced_adaptive_workflow():
    """Test the complete enhanced adaptive learning workflow."""
    print("🧠 Testing Enhanced Adaptive Learning Workflow")
    print("=" * 60)
    
    student_id = "enhanced_test_student"
    concept_id = "algebra_basics"
    
    try:
        async with sse_client(SERVER_URL) as (sse, write):
            async with ClientSession(sse, write) as session:
                await session.initialize()
                
                # Step 1: Start adaptive session
                print("\n🚀 Step 1: Starting adaptive session...")
                session_result = await session.call_tool("start_adaptive_session", {
                    "student_id": student_id,
                    "concept_id": concept_id,
                    "initial_difficulty": 0.5
                })
                session_data = await extract_response_content(session_result)
                print(f"✅ Session started: {session_data.get('session_id', 'N/A')}")
                
                if not session_data.get("success"):
                    print("❌ Failed to start session")
                    return
                
                session_id = session_data.get("session_id")
                
                # Step 2: Record learning events
                print("\n📝 Step 2: Recording learning events...")
                
                # Record correct answer
                event_result1 = await session.call_tool("record_learning_event", {
                    "student_id": student_id,
                    "concept_id": concept_id,
                    "session_id": session_id,
                    "event_type": "answer_correct",
                    "event_data": {"time_taken": 25, "difficulty": 0.5}
                })
                event_data1 = await extract_response_content(event_result1)
                print(f"✅ Correct answer recorded - New mastery: {event_data1.get('updated_mastery', 'N/A')}")
                
                # Record incorrect answer
                event_result2 = await session.call_tool("record_learning_event", {
                    "student_id": student_id,
                    "concept_id": concept_id,
                    "session_id": session_id,
                    "event_type": "answer_incorrect",
                    "event_data": {"time_taken": 45, "difficulty": 0.5}
                })
                event_data2 = await extract_response_content(event_result2)
                print(f"❌ Incorrect answer recorded - New mastery: {event_data2.get('updated_mastery', 'N/A')}")
                
                # Step 3: Get recommendations
                print("\n💡 Step 3: Getting adaptive recommendations...")
                rec_result = await session.call_tool("get_adaptive_recommendations", {
                    "student_id": student_id,
                    "concept_id": concept_id,
                    "session_id": session_id
                })
                rec_data = await extract_response_content(rec_result)
                print(f"📊 Current mastery: {rec_data.get('current_mastery', 'N/A')}")
                print(f"⚡ Current difficulty: {rec_data.get('current_difficulty', 'N/A')}")
                
                # Step 4: Generate adaptive learning path
                print("\n🛤️ Step 4: Generating adaptive learning path...")
                path_result = await session.call_tool("get_adaptive_learning_path", {
                    "student_id": student_id,
                    "target_concepts": ["algebra_basics", "linear_equations", "quadratic_equations"],
                    "strategy": "adaptive",
                    "max_concepts": 5
                })
                path_data = await extract_response_content(path_result)
                print(f"📋 Learning path generated with {len(path_data.get('learning_path', []))} steps")
                print(f"⏱️ Total estimated time: {path_data.get('total_time_minutes', 'N/A')} minutes")
                
                # Step 5: Get progress summary
                print("\n📊 Step 5: Getting progress summary...")
                progress_result = await session.call_tool("get_student_progress_summary", {
                    "student_id": student_id,
                    "days": 7
                })
                progress_data = await extract_response_content(progress_result)
                summary = progress_data.get('summary', {})
                print(f"📈 Concepts practiced: {summary.get('concepts_practiced', 'N/A')}")
                print(f"⏱️ Total time: {summary.get('total_time_minutes', 'N/A')} minutes")
                print(f"🎯 Average mastery: {summary.get('average_mastery', 'N/A')}")
                
                print("\n✅ Enhanced adaptive learning workflow completed successfully!")
                print("\n🎉 All features working perfectly!")
                
    except Exception as e:
        print(f"❌ Error during workflow: {e}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_adaptive_workflow())
