"""
Learning path generation tools for TutorX with adaptive learning capabilities.
"""
import random
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path
import json
import re
from dataclasses import dataclass, asdict
from enum import Enum

# Add the parent directory to the Python path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent.parent
sys.path.insert(0, str(parent_dir))

# Import from local resources
from resources.concept_graph import CONCEPT_GRAPH

# Import MCP
from mcp_server.mcp_instance import mcp
from mcp_server.model.gemini_flash import GeminiFlash

MODEL = GeminiFlash()

# Adaptive Learning Data Structures
class DifficultyLevel(Enum):
    VERY_EASY = 0.2
    EASY = 0.4
    MEDIUM = 0.6
    HARD = 0.8
    VERY_HARD = 1.0

@dataclass
class StudentPerformance:
    student_id: str
    concept_id: str
    accuracy_rate: float = 0.0
    time_spent_minutes: float = 0.0
    attempts_count: int = 0
    mastery_level: float = 0.0
    last_accessed: datetime = None
    difficulty_preference: float = 0.5

@dataclass
class LearningEvent:
    student_id: str
    concept_id: str
    event_type: str  # 'answer_correct', 'answer_incorrect', 'hint_used', 'time_spent'
    timestamp: datetime
    data: Dict[str, Any]

# In-memory storage for adaptive learning
student_performances: Dict[str, Dict[str, StudentPerformance]] = {}
learning_events: List[LearningEvent] = []
active_sessions: Dict[str, Dict[str, Any]] = {}

def get_prerequisites(concept_id: str, visited: Optional[set] = None) -> List[Dict[str, Any]]:
    """
    Get all prerequisites for a concept recursively.
    
    Args:
        concept_id: ID of the concept to get prerequisites for
        visited: Set of already visited concepts to avoid cycles
        
    Returns:
        List of prerequisite concepts in order
    """
    if visited is None:
        visited = set()
    
    if concept_id not in CONCEPT_GRAPH or concept_id in visited:
        return []
    
    visited.add(concept_id)
    prerequisites = []
    
    # Get direct prerequisites
    for prereq_id in CONCEPT_GRAPH[concept_id].get("prerequisites", []):
        if prereq_id in CONCEPT_GRAPH and prereq_id not in visited:
            prerequisites.extend(get_prerequisites(prereq_id, visited))
    
    # Add the current concept
    prerequisites.append(CONCEPT_GRAPH[concept_id])
    return prerequisites

def generate_learning_path(concept_ids: List[str], student_level: str = "beginner") -> Dict[str, Any]:
    """
    Generate a personalized learning path for a student.
    
    Args:
        concept_ids: List of concept IDs to include in the learning path
        student_level: Student's current level (beginner, intermediate, advanced)
        
    Returns:
        Dictionary containing the learning path
    """
    if not concept_ids:
        return {"error": "At least one concept ID is required"}
    
    # Get all prerequisites for each concept
    all_prerequisites = []
    visited = set()
    
    for concept_id in concept_ids:
        if concept_id in CONCEPT_GRAPH:
            prereqs = get_prerequisites(concept_id, visited)
            all_prerequisites.extend(prereqs)
    
    # Remove duplicates while preserving order
    unique_concepts = []
    seen = set()
    for concept in all_prerequisites:
        if concept["id"] not in seen:
            seen.add(concept["id"])
            unique_concepts.append(concept)
    
    # Add any target concepts not already in the path
    for concept_id in concept_ids:
        if concept_id in CONCEPT_GRAPH and concept_id not in seen:
            unique_concepts.append(CONCEPT_GRAPH[concept_id])
    
    # Estimate time required for each concept based on student level
    time_estimates = {
        "beginner": {"min": 30, "max": 60},    # 30-60 minutes per concept
        "intermediate": {"min": 20, "max": 45},  # 20-45 minutes per concept
        "advanced": {"min": 15, "max": 30}      # 15-30 minutes per concept
    }
    
    level = student_level.lower()
    if level not in time_estimates:
        level = "beginner"
    
    time_min = time_estimates[level]["min"]
    time_max = time_estimates[level]["max"]
    
    # Generate learning path with estimated times
    learning_path = []
    total_minutes = 0
    
    for i, concept in enumerate(unique_concepts, 1):
        # Random time estimate within range
        minutes = random.randint(time_min, time_max)
        total_minutes += minutes
        
        learning_path.append({
            "step": i,
            "concept_id": concept["id"],
            "concept_name": concept["name"],
            "description": concept.get("description", ""),
            "estimated_time_minutes": minutes,
            "resources": [
                f"Video tutorial on {concept['name']}",
                f"{concept['name']} documentation",
                f"Practice exercises for {concept['name']}"
            ]
        })
    
    # Calculate total time
    hours, minutes = divmod(total_minutes, 60)
    total_time = f"{hours}h {minutes}m" if hours > 0 else f"{minutes}m"
    
    return {
        "learning_path": learning_path,
        "total_steps": len(learning_path),
        "total_time_minutes": total_minutes,
        "total_time_display": total_time,
        "student_level": student_level,
        "generated_at": datetime.utcnow().isoformat() + "Z"
    }

def clean_json_trailing_commas(json_text: str) -> str:
    return re.sub(r',([ \t\r\n]*[}}\]])', r'\1', json_text)

def extract_json_from_text(text: str):
    if not text or not isinstance(text, str):
        return None
    # Remove code fences
    text = re.sub(r'^\s*```(?:json)?\s*', '', text, flags=re.IGNORECASE)
    text = re.sub(r'\s*```\s*$', '', text, flags=re.IGNORECASE)
    text = text.strip()
    # Remove trailing commas
    cleaned = clean_json_trailing_commas(text)
    return json.loads(cleaned)

# Adaptive Learning Helper Functions
def get_student_performance(student_id: str, concept_id: str) -> StudentPerformance:
    """Get or create student performance record."""
    if student_id not in student_performances:
        student_performances[student_id] = {}

    if concept_id not in student_performances[student_id]:
        student_performances[student_id][concept_id] = StudentPerformance(
            student_id=student_id,
            concept_id=concept_id,
            last_accessed=datetime.utcnow()
        )

    return student_performances[student_id][concept_id]

def update_mastery_level(performance: StudentPerformance) -> float:
    """Calculate mastery level based on performance metrics."""
    if performance.attempts_count == 0:
        return 0.0

    # Weighted calculation: accuracy (60%), consistency (20%), efficiency (20%)
    accuracy_score = performance.accuracy_rate

    # Consistency: higher attempts with stable accuracy indicate consistency
    consistency_score = min(1.0, performance.attempts_count / 5.0) if performance.accuracy_rate > 0.7 else 0.5

    # Efficiency: less time per attempt indicates better understanding
    avg_time = performance.time_spent_minutes / performance.attempts_count if performance.attempts_count > 0 else 30
    efficiency_score = max(0.1, 1.0 - (avg_time - 10) / 50)  # Normalize around 10-60 minutes

    mastery = (accuracy_score * 0.6) + (consistency_score * 0.2) + (efficiency_score * 0.2)
    performance.mastery_level = min(1.0, max(0.0, mastery))
    return performance.mastery_level

def adapt_difficulty(performance: StudentPerformance) -> float:
    """Adapt difficulty based on student performance."""
    if performance.attempts_count < 2:
        return performance.difficulty_preference

    # If accuracy is high, increase difficulty
    if performance.accuracy_rate > 0.8:
        new_difficulty = min(1.0, performance.difficulty_preference + 0.1)
    # If accuracy is low, decrease difficulty
    elif performance.accuracy_rate < 0.5:
        new_difficulty = max(0.2, performance.difficulty_preference - 0.1)
    else:
        new_difficulty = performance.difficulty_preference

    performance.difficulty_preference = new_difficulty
    return new_difficulty

# Adaptive Learning MCP Tools

@mcp.tool()
async def start_adaptive_session(student_id: str, concept_id: str, initial_difficulty: float = 0.5) -> dict:
    """
    Start an adaptive learning session for a student.

    Args:
        student_id: Unique identifier for the student
        concept_id: Concept being learned
        initial_difficulty: Initial difficulty level (0.0 to 1.0)

    Returns:
        Session information and initial recommendations
    """
    try:
        session_id = f"{student_id}_{concept_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Get or create student performance
        performance = get_student_performance(student_id, concept_id)
        performance.difficulty_preference = initial_difficulty
        performance.last_accessed = datetime.utcnow()

        # Create session
        active_sessions[session_id] = {
            'student_id': student_id,
            'concept_id': concept_id,
            'start_time': datetime.utcnow(),
            'current_difficulty': initial_difficulty,
            'events': [],
            'questions_answered': 0,
            'correct_answers': 0
        }

        return {
            "success": True,
            "session_id": session_id,
            "student_id": student_id,
            "concept_id": concept_id,
            "initial_difficulty": initial_difficulty,
            "current_mastery": performance.mastery_level,
            "recommendations": [
                f"Start with difficulty level {initial_difficulty:.1f}",
                f"Current mastery level: {performance.mastery_level:.2f}",
                "System will adapt based on your performance"
            ]
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
async def record_learning_event(student_id: str, concept_id: str, session_id: str,
                               event_type: str, event_data: dict) -> dict:
    """
    Record a learning event for adaptive analysis.

    Args:
        student_id: Student identifier
        concept_id: Concept identifier
        session_id: Session identifier
        event_type: Type of event ('answer_correct', 'answer_incorrect', 'hint_used', 'time_spent')
        event_data: Additional event data

    Returns:
        Event recording confirmation and updated recommendations
    """
    try:
        # Record the event
        event = LearningEvent(
            student_id=student_id,
            concept_id=concept_id,
            event_type=event_type,
            timestamp=datetime.utcnow(),
            data=event_data
        )
        learning_events.append(event)

        # Update session
        if session_id in active_sessions:
            session = active_sessions[session_id]
            session['events'].append(event)

            if event_type in ['answer_correct', 'answer_incorrect']:
                session['questions_answered'] += 1
                if event_type == 'answer_correct':
                    session['correct_answers'] += 1

        # Update student performance
        performance = get_student_performance(student_id, concept_id)
        performance.attempts_count += 1

        if event_type == 'answer_correct':
            performance.accuracy_rate = (performance.accuracy_rate * (performance.attempts_count - 1) + 1.0) / performance.attempts_count
        elif event_type == 'answer_incorrect':
            performance.accuracy_rate = (performance.accuracy_rate * (performance.attempts_count - 1) + 0.0) / performance.attempts_count
        elif event_type == 'time_spent':
            performance.time_spent_minutes += event_data.get('minutes', 0)

        # Update mastery level
        new_mastery = update_mastery_level(performance)

        # Adapt difficulty
        new_difficulty = adapt_difficulty(performance)

        # Generate recommendations
        recommendations = []
        if performance.accuracy_rate > 0.8 and performance.attempts_count >= 3:
            recommendations.append("Great job! Consider moving to a harder difficulty level.")
        elif performance.accuracy_rate < 0.5 and performance.attempts_count >= 3:
            recommendations.append("Let's try some easier questions to build confidence.")

        if new_mastery > 0.8:
            recommendations.append("You're mastering this concept! Ready for the next one?")

        return {
            "success": True,
            "event_recorded": True,
            "updated_mastery": new_mastery,
            "updated_difficulty": new_difficulty,
            "current_accuracy": performance.accuracy_rate,
            "recommendations": recommendations
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
async def get_adaptive_recommendations(student_id: str, concept_id: str, session_id: str = None) -> dict:
    """
    Get adaptive learning recommendations for a student.

    Args:
        student_id: Student identifier
        concept_id: Concept identifier
        session_id: Optional session identifier

    Returns:
        Adaptive learning recommendations
    """
    try:
        performance = get_student_performance(student_id, concept_id)

        recommendations = []

        # Difficulty recommendations
        if performance.accuracy_rate > 0.8:
            recommendations.append({
                "type": "difficulty_increase",
                "message": "Consider increasing difficulty level",
                "suggested_difficulty": min(1.0, performance.difficulty_preference + 0.1)
            })
        elif performance.accuracy_rate < 0.5:
            recommendations.append({
                "type": "difficulty_decrease",
                "message": "Consider decreasing difficulty level",
                "suggested_difficulty": max(0.2, performance.difficulty_preference - 0.1)
            })

        # Mastery recommendations
        if performance.mastery_level > 0.8:
            recommendations.append({
                "type": "concept_mastery",
                "message": "Concept mastered! Ready for next concept",
                "next_action": "advance_to_next_concept"
            })
        elif performance.mastery_level < 0.3 and performance.attempts_count >= 5:
            recommendations.append({
                "type": "additional_practice",
                "message": "More practice needed for this concept",
                "next_action": "provide_additional_resources"
            })

        return {
            "success": True,
            "student_id": student_id,
            "concept_id": concept_id,
            "current_mastery": performance.mastery_level,
            "current_difficulty": performance.difficulty_preference,
            "accuracy_rate": performance.accuracy_rate,
            "recommendations": recommendations
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
async def get_learning_path(student_id: str, concept_ids: list, student_level: str = "beginner") -> dict:
    """
    Generate a personalized learning path for a student, fully LLM-driven.
    Use Gemini to generate a JSON object with a list of steps, each with concept name, description, estimated time, and recommended resources.
    """
    prompt = (
        f"A student (ID: {student_id}) with level '{student_level}' needs a learning path for these concepts: {concept_ids}. "
        f"Return a JSON object with a 'learning_path' field: a list of steps, each with concept_name, description, estimated_time_minutes, and resources (list)."
    )
    llm_response = await MODEL.generate_text(prompt)
    try:
        data = extract_json_from_text(llm_response)
    except Exception:
        data = {"llm_raw": llm_response, "error": "Failed to parse LLM output as JSON"}
    return data

@mcp.tool()
async def get_adaptive_learning_path(student_id: str, target_concepts: list,
                                   strategy: str = "adaptive", max_concepts: int = 10) -> dict:
    """
    Generate an adaptive learning path based on student performance and preferences.

    Args:
        student_id: Student identifier
        target_concepts: List of target concept IDs
        strategy: Learning strategy ('adaptive', 'mastery_focused', 'quick_review')
        max_concepts: Maximum number of concepts in the path

    Returns:
        Optimized adaptive learning path
    """
    try:
        # Get student performance data
        student_data = {}
        for concept_id in target_concepts:
            if student_id in student_performances and concept_id in student_performances[student_id]:
                perf = student_performances[student_id][concept_id]
                student_data[concept_id] = {
                    'mastery_level': perf.mastery_level,
                    'accuracy_rate': perf.accuracy_rate,
                    'difficulty_preference': perf.difficulty_preference,
                    'attempts_count': perf.attempts_count
                }

        # Generate adaptive path based on strategy
        if strategy == "mastery_focused":
            # Focus on concepts with low mastery first
            sorted_concepts = sorted(target_concepts,
                                   key=lambda c: student_data.get(c, {}).get('mastery_level', 0))
        elif strategy == "quick_review":
            # Focus on concepts with high mastery for quick review
            sorted_concepts = sorted(target_concepts,
                                   key=lambda c: student_data.get(c, {}).get('mastery_level', 0),
                                   reverse=True)
        else:  # adaptive
            # Balance between mastery level and difficulty preference
            def adaptive_score(concept_id):
                data = student_data.get(concept_id, {})
                mastery = data.get('mastery_level', 0)
                attempts = data.get('attempts_count', 0)
                # Prioritize concepts with some attempts but low mastery
                return (1 - mastery) * (1 + min(attempts / 10, 1))

            sorted_concepts = sorted(target_concepts, key=adaptive_score, reverse=True)

        # Limit to max_concepts
        selected_concepts = sorted_concepts[:max_concepts]

        # Generate learning path with adaptive recommendations
        learning_path = []
        for i, concept_id in enumerate(selected_concepts, 1):
            concept_data = CONCEPT_GRAPH.get(concept_id, {"name": concept_id, "description": ""})
            perf_data = student_data.get(concept_id, {})

            # Estimate time based on mastery level
            base_time = 30  # Base 30 minutes
            mastery = perf_data.get('mastery_level', 0)
            if mastery > 0.8:
                estimated_time = base_time * 0.5  # Quick review
            elif mastery > 0.5:
                estimated_time = base_time * 0.8  # Moderate practice
            else:
                estimated_time = base_time * 1.2  # More practice needed

            learning_path.append({
                "step": i,
                "concept_id": concept_id,
                "concept_name": concept_data.get("name", concept_id),
                "description": concept_data.get("description", ""),
                "estimated_time_minutes": int(estimated_time),
                "current_mastery": perf_data.get('mastery_level', 0),
                "recommended_difficulty": perf_data.get('difficulty_preference', 0.5),
                "adaptive_notes": _get_adaptive_notes(perf_data),
                "resources": [
                    f"Adaptive practice for {concept_data.get('name', concept_id)}",
                    f"Personalized exercises at {perf_data.get('difficulty_preference', 0.5):.1f} difficulty",
                    f"Progress tracking and real-time feedback"
                ]
            })

        total_time = sum(step["estimated_time_minutes"] for step in learning_path)

        return {
            "success": True,
            "learning_path": learning_path,
            "strategy": strategy,
            "total_steps": len(learning_path),
            "total_time_minutes": total_time,
            "student_id": student_id,
            "adaptive_features": [
                "Real-time difficulty adjustment",
                "Mastery-based progression",
                "Personalized time estimates",
                "Performance-driven recommendations"
            ],
            "generated_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def _get_adaptive_notes(perf_data: dict) -> str:
    """Generate adaptive notes based on performance data."""
    mastery = perf_data.get('mastery_level', 0)
    accuracy = perf_data.get('accuracy_rate', 0)
    attempts = perf_data.get('attempts_count', 0)

    if attempts == 0:
        return "New concept - start with guided practice"
    elif mastery > 0.8:
        return "Well mastered - quick review recommended"
    elif mastery > 0.5:
        return "Good progress - continue with current difficulty"
    elif accuracy < 0.5:
        return "Needs more practice - consider easier difficulty"
    else:
        return "Building understanding - maintain current approach"

@mcp.tool()
async def get_student_progress_summary(student_id: str, days: int = 7) -> dict:
    """
    Get a comprehensive progress summary for a student.

    Args:
        student_id: Student identifier
        days: Number of days to analyze

    Returns:
        Progress summary with analytics
    """
    try:
        # Get student performance data
        if student_id not in student_performances:
            return {
                "success": True,
                "student_id": student_id,
                "message": "No performance data available",
                "concepts_practiced": 0,
                "total_time_minutes": 0,
                "average_mastery": 0.0
            }

        student_data = student_performances[student_id]

        # Calculate summary statistics
        total_concepts = len(student_data)
        total_time = sum(perf.time_spent_minutes for perf in student_data.values())
        total_attempts = sum(perf.attempts_count for perf in student_data.values())
        average_mastery = sum(perf.mastery_level for perf in student_data.values()) / total_concepts if total_concepts > 0 else 0
        average_accuracy = sum(perf.accuracy_rate for perf in student_data.values()) / total_concepts if total_concepts > 0 else 0

        # Get recent events
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        recent_events = [e for e in learning_events
                        if e.student_id == student_id and e.timestamp >= cutoff_date]

        # Concept breakdown
        concept_summary = []
        for concept_id, perf in student_data.items():
            concept_summary.append({
                "concept_id": concept_id,
                "mastery_level": perf.mastery_level,
                "accuracy_rate": perf.accuracy_rate,
                "time_spent_minutes": perf.time_spent_minutes,
                "attempts_count": perf.attempts_count,
                "last_accessed": perf.last_accessed.isoformat() if perf.last_accessed else None,
                "status": _get_concept_status(perf.mastery_level)
            })

        return {
            "success": True,
            "student_id": student_id,
            "analysis_period_days": days,
            "summary": {
                "concepts_practiced": total_concepts,
                "total_time_minutes": total_time,
                "total_attempts": total_attempts,
                "average_mastery": round(average_mastery, 2),
                "average_accuracy": round(average_accuracy, 2),
                "recent_events_count": len(recent_events)
            },
            "concept_breakdown": concept_summary,
            "recommendations": _generate_progress_recommendations(student_data),
            "generated_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def _get_concept_status(mastery_level: float) -> str:
    """Get concept status based on mastery level."""
    if mastery_level >= 0.8:
        return "Mastered"
    elif mastery_level >= 0.6:
        return "Good Progress"
    elif mastery_level >= 0.4:
        return "Learning"
    elif mastery_level >= 0.2:
        return "Struggling"
    else:
        return "Needs Attention"

def _generate_progress_recommendations(student_data: Dict[str, StudentPerformance]) -> List[str]:
    """Generate recommendations based on student progress."""
    recommendations = []

    mastered_concepts = [cid for cid, perf in student_data.items() if perf.mastery_level >= 0.8]
    struggling_concepts = [cid for cid, perf in student_data.items() if perf.mastery_level < 0.4]

    if len(mastered_concepts) > 0:
        recommendations.append(f"Great job! You've mastered {len(mastered_concepts)} concepts.")

    if len(struggling_concepts) > 0:
        recommendations.append(f"Focus on {len(struggling_concepts)} concepts that need more practice.")

    # Check for concepts that haven't been accessed recently
    week_ago = datetime.utcnow() - timedelta(days=7)
    stale_concepts = [cid for cid, perf in student_data.items()
                     if perf.last_accessed and perf.last_accessed < week_ago]

    if len(stale_concepts) > 0:
        recommendations.append(f"Consider reviewing {len(stale_concepts)} concepts you haven't practiced recently.")

    return recommendations
